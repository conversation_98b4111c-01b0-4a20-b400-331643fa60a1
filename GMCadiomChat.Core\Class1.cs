﻿using GMCadiomChat.Shared.Enums;

namespace GMCadiomChat.Core.Entities;

public class Client
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public UserStatus Status { get; set; }
    public DateTime LastSeen { get; set; }
    public DateTime CreatedAt { get; set; }
    public string ConnectionId { get; set; } = string.Empty;

    // Navigation properties
    public virtual ICollection<ChatMessage> SentMessages { get; set; } = new List<ChatMessage>();
    public virtual ICollection<ChatMessage> ReceivedMessages { get; set; } = new List<ChatMessage>();

    public Client()
    {
        Id = Guid.NewGuid();
        CreatedAt = DateTime.UtcNow;
        LastSeen = DateTime.UtcNow;
        Status = UserStatus.Offline;
    }
}
