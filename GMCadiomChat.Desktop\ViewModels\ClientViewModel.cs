using CommunityToolkit.Mvvm.ComponentModel;
using GMCadiomChat.Shared.DTOs;
using GMCadiomChat.Shared.Enums;
using System;

namespace GMCadiomChat.Desktop.ViewModels;

public partial class ClientViewModel : ObservableObject
{
    [ObservableProperty]
    private Guid id;

    [ObservableProperty]
    private string username = string.Empty;

    [ObservableProperty]
    private string email = string.Empty;

    [ObservableProperty]
    private UserStatus status;

    [ObservableProperty]
    private DateTime lastSeen;

    [ObservableProperty]
    private string connectionId = string.Empty;

    [ObservableProperty]
    private bool isSelected;

    [ObservableProperty]
    private int unreadCount;

    [ObservableProperty]
    private string statusDisplay = string.Empty;

    [ObservableProperty]
    private string statusColor = string.Empty;

    [ObservableProperty]
    private string lastSeenDisplay = string.Empty;

    public ClientViewModel()
    {
        UpdateStatusDisplay();
        UpdateLastSeenDisplay();
    }

    public ClientViewModel(ClientDto client) : this()
    {
        Id = client.Id;
        Username = client.Username;
        Email = client.Email;
        Status = client.Status;
        LastSeen = client.LastSeen;
        ConnectionId = client.ConnectionId;
        
        UpdateStatusDisplay();
        UpdateLastSeenDisplay();
    }

    partial void OnStatusChanged(UserStatus value)
    {
        UpdateStatusDisplay();
    }

    partial void OnLastSeenChanged(DateTime value)
    {
        UpdateLastSeenDisplay();
    }

    private void UpdateStatusDisplay()
    {
        StatusDisplay = Status switch
        {
            UserStatus.Online => "Online",
            UserStatus.Away => "Away",
            UserStatus.Busy => "Busy",
            UserStatus.Offline => "Offline",
            _ => "Unknown"
        };

        StatusColor = Status switch
        {
            UserStatus.Online => "#4CAF50",    // Green
            UserStatus.Away => "#FF9800",      // Orange
            UserStatus.Busy => "#F44336",      // Red
            UserStatus.Offline => "#9E9E9E",   // Gray
            _ => "#9E9E9E"
        };
    }

    private void UpdateLastSeenDisplay()
    {
        if (Status == UserStatus.Online)
        {
            LastSeenDisplay = "Online";
            return;
        }

        var now = DateTime.Now;
        var lastSeenLocal = LastSeen.ToLocalTime();
        var timeDiff = now - lastSeenLocal;

        LastSeenDisplay = timeDiff.TotalMinutes switch
        {
            < 1 => "Just now",
            < 60 => $"{(int)timeDiff.TotalMinutes} minutes ago",
            < 1440 => $"{(int)timeDiff.TotalHours} hours ago",
            < 10080 => $"{(int)timeDiff.TotalDays} days ago",
            _ => lastSeenLocal.ToString("MMM dd, yyyy")
        };
    }

    public void UpdateFrom(ClientDto client)
    {
        Username = client.Username;
        Email = client.Email;
        Status = client.Status;
        LastSeen = client.LastSeen;
        ConnectionId = client.ConnectionId;
    }

    public bool IsOnline => Status == UserStatus.Online;

    public string DisplayName => string.IsNullOrEmpty(Username) ? Email : Username;

    public string UnreadCountDisplay => UnreadCount > 0 ? UnreadCount.ToString() : string.Empty;

    public bool HasUnreadMessages => UnreadCount > 0;
}
