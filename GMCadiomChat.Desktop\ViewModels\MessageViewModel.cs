using CommunityToolkit.Mvvm.ComponentModel;
using GMCadiomChat.Shared.DTOs;
using GMCadiomChat.Shared.Enums;
using System;

namespace GMCadiomChat.Desktop.ViewModels;

public partial class MessageViewModel : ObservableObject
{
    [ObservableProperty]
    private Guid id;

    [ObservableProperty]
    private Guid senderId;

    [ObservableProperty]
    private string senderUsername = string.Empty;

    [ObservableProperty]
    private Guid? receiverId;

    [ObservableProperty]
    private string? receiverUsername;

    [ObservableProperty]
    private DateTime timestamp;

    [ObservableProperty]
    private MessageType messageType;

    [ObservableProperty]
    private string content = string.Empty;

    [ObservableProperty]
    private string? filePath;

    [ObservableProperty]
    private long? fileSize;

    [ObservableProperty]
    private string? fileFormat;

    [ObservableProperty]
    private int? duration;

    [ObservableProperty]
    private bool isDelivered;

    [ObservableProperty]
    private bool isRead;

    [ObservableProperty]
    private bool isFromCurrentUser;

    [ObservableProperty]
    private string displayTime = string.Empty;

    [ObservableProperty]
    private string statusIcon = string.Empty;

    public MessageViewModel()
    {
        UpdateDisplayTime();
        UpdateStatusIcon();
    }

    public MessageViewModel(MessageDto message, Guid currentUserId) : this()
    {
        Id = message.Id;
        SenderId = message.SenderId;
        SenderUsername = message.SenderUsername;
        ReceiverId = message.ReceiverId;
        ReceiverUsername = message.ReceiverUsername;
        Timestamp = message.Timestamp;
        MessageType = message.MessageType;
        Content = message.Content;
        FilePath = message.FilePath;
        FileSize = message.FileSize;
        FileFormat = message.FileFormat;
        Duration = message.Duration;
        IsDelivered = message.IsDelivered;
        IsRead = message.IsRead;
        IsFromCurrentUser = message.SenderId == currentUserId;
        
        UpdateDisplayTime();
        UpdateStatusIcon();
    }

    partial void OnTimestampChanged(DateTime value)
    {
        UpdateDisplayTime();
    }

    partial void OnIsDeliveredChanged(bool value)
    {
        UpdateStatusIcon();
    }

    partial void OnIsReadChanged(bool value)
    {
        UpdateStatusIcon();
    }

    private void UpdateDisplayTime()
    {
        var now = DateTime.Now;
        var messageTime = Timestamp.ToLocalTime();

        if (messageTime.Date == now.Date)
        {
            DisplayTime = messageTime.ToString("HH:mm");
        }
        else if (messageTime.Date == now.Date.AddDays(-1))
        {
            DisplayTime = $"Yesterday {messageTime:HH:mm}";
        }
        else if (messageTime.Date > now.Date.AddDays(-7))
        {
            DisplayTime = $"{messageTime:ddd HH:mm}";
        }
        else
        {
            DisplayTime = messageTime.ToString("MMM dd, HH:mm");
        }
    }

    private void UpdateStatusIcon()
    {
        if (!IsFromCurrentUser)
        {
            StatusIcon = string.Empty;
            return;
        }

        StatusIcon = IsRead ? "✓✓" : IsDelivered ? "✓" : "⏳";
    }

    public string GetDisplayContent()
    {
        return MessageType switch
        {
            MessageType.Text => Content,
            MessageType.Image => $"📷 {Content}",
            MessageType.Audio => $"🎵 Audio ({Duration}s)",
            MessageType.ScreenShare => $"🖥️ {Content}",
            MessageType.Buzz => "📳 Buzz!",
            _ => Content
        };
    }

    public bool HasFile => !string.IsNullOrEmpty(FilePath);

    public string FileSizeDisplay => FileSize.HasValue ? FormatFileSize(FileSize.Value) : string.Empty;

    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}
