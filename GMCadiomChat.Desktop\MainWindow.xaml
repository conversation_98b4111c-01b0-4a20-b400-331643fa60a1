﻿<Window x:Class="GMCadiomChat.Desktop.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GMCadiomChat.Desktop"
        xmlns:vm="clr-namespace:GMCadiomChat.Desktop.ViewModels"
        mc:Ignorable="d"
        Title="GMCadiomChat" Height="700" Width="1200" MinHeight="500" MinWidth="800"
        WindowStartupLocation="CenterScreen">

    <Window.DataContext>
        <vm:ChatViewModel />
    </Window.DataContext>

    <Window.Resources>
        <!-- Modern color scheme -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#1976D2"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
        <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
        <SolidColorBrush x:Key="DividerBrush" Color="#E0E0E0"/>

        <!-- Button styles -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- TextBox styles -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderBrush" Value="{StaticResource DividerBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="GMCadiomChat"
                             FontSize="20"
                             FontWeight="Bold"
                             Foreground="White"
                             VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding ConnectionStatus}"
                             FontSize="12"
                             Foreground="White"
                             Opacity="0.8"
                             Margin="16,0,0,0"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBox x:Name="UsernameTextBox"
                           Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                           Style="{StaticResource ModernTextBoxStyle}"
                           Width="150"
                           IsEnabled="{Binding IsConnected, Converter={x:Static local:Converters.InverseBooleanConverter}}"
                           VerticalAlignment="Center"/>

                    <Button Content="{Binding IsConnected, Converter={x:Static local:Converters.ConnectButtonTextConverter}}"
                          Command="{Binding IsConnected, Converter={x:Static local:Converters.ConnectCommandConverter}}"
                          Style="{StaticResource ModernButtonStyle}"
                          Margin="8,0,0,0"
                          VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" MinWidth="250"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*" MinWidth="400"/>
            </Grid.ColumnDefinitions>

            <!-- Users List -->
            <Border Grid.Column="0" Background="{StaticResource SurfaceBrush}" BorderBrush="{StaticResource DividerBrush}" BorderThickness="0,0,1,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="{StaticResource BackgroundBrush}" Padding="16,12" BorderBrush="{StaticResource DividerBrush}" BorderThickness="0,0,0,1">
                        <TextBlock Text="Online Users" FontWeight="SemiBold" FontSize="14" Foreground="{StaticResource TextPrimaryBrush}"/>
                    </Border>

                    <ListBox Grid.Row="1"
                           ItemsSource="{Binding OnlineUsers}"
                           SelectedItem="{Binding SelectedUser}"
                           Background="Transparent"
                           BorderThickness="0"
                           ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Padding="16,12" Background="Transparent" Cursor="Hand">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Status indicator -->
                                        <Ellipse Grid.Column="0"
                                               Width="12" Height="12"
                                               Fill="{Binding StatusColor}"
                                               VerticalAlignment="Top"
                                               Margin="0,4,12,0"/>

                                        <!-- User info -->
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding DisplayName}"
                                                     FontWeight="Medium"
                                                     FontSize="14"
                                                     Foreground="{StaticResource TextPrimaryBrush}"/>
                                            <TextBlock Text="{Binding StatusDisplay}"
                                                     FontSize="12"
                                                     Foreground="{StaticResource TextSecondaryBrush}"/>
                                        </StackPanel>

                                        <!-- Unread count -->
                                        <Border Grid.Column="2"
                                              Background="{StaticResource AccentBrush}"
                                              CornerRadius="10"
                                              MinWidth="20" Height="20"
                                              Visibility="{Binding HasUnreadMessages, Converter={x:Static local:Converters.BooleanToVisibilityConverter}}"
                                              VerticalAlignment="Top">
                                            <TextBlock Text="{Binding UnreadCountDisplay}"
                                                     Foreground="White"
                                                     FontSize="10"
                                                     FontWeight="Bold"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"/>
                                        </Border>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Padding" Value="0"/>
                                <Setter Property="Margin" Value="0"/>
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
                                    </Trigger>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </ListBox.ItemContainerStyle>
                    </ListBox>
                </Grid>
            </Border>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Width="4" Background="{StaticResource DividerBrush}" HorizontalAlignment="Center"/>

            <!-- Chat Area -->
            <Grid Grid.Column="2" Background="{StaticResource SurfaceBrush}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Chat Header -->
                <Border Grid.Row="0" Background="{StaticResource BackgroundBrush}" Padding="16,12" BorderBrush="{StaticResource DividerBrush}" BorderThickness="0,0,0,1">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0">
                            <TextBlock Text="{Binding SelectedUser.DisplayName, FallbackValue='Select a user to start chatting'}"
                                     FontWeight="SemiBold"
                                     FontSize="16"
                                     Foreground="{StaticResource TextPrimaryBrush}"/>
                            <TextBlock Text="{Binding SelectedUser.LastSeenDisplay}"
                                     FontSize="12"
                                     Foreground="{StaticResource TextSecondaryBrush}"/>
                        </StackPanel>

                        <Button Grid.Column="1"
                              Content="📳 Buzz"
                              Command="{Binding SendBuzzCommand}"
                              Style="{StaticResource ModernButtonStyle}"
                              Background="{StaticResource AccentBrush}"
                              IsEnabled="{Binding SelectedUser, Converter={x:Static local:Converters.NotNullToBooleanConverter}}"/>
                    </Grid>
                </Border>

                <!-- Messages -->
                <ScrollViewer Grid.Row="1"
                            x:Name="MessagesScrollViewer"
                            VerticalScrollBarVisibility="Auto"
                            HorizontalScrollBarVisibility="Disabled"
                            Padding="16">
                    <ItemsControl ItemsSource="{Binding Messages}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Margin="0,4"
                                      HorizontalAlignment="{Binding IsFromCurrentUser, Converter={x:Static local:Converters.MessageAlignmentConverter}}"
                                      MaxWidth="400">
                                    <Border Background="{Binding IsFromCurrentUser, Converter={x:Static local:Converters.MessageBackgroundConverter}}"
                                          CornerRadius="12"
                                          Padding="12,8">
                                        <StackPanel>
                                            <TextBlock Text="{Binding GetDisplayContent()}"
                                                     Foreground="{Binding IsFromCurrentUser, Converter={x:Static local:Converters.MessageForegroundConverter}}"
                                                     TextWrapping="Wrap"/>
                                            <StackPanel Orientation="Horizontal"
                                                      HorizontalAlignment="Right"
                                                      Margin="0,4,0,0">
                                                <TextBlock Text="{Binding DisplayTime}"
                                                         FontSize="10"
                                                         Foreground="{Binding IsFromCurrentUser, Converter={x:Static local:Converters.MessageTimeForegroundConverter}}"
                                                         Opacity="0.7"/>
                                                <TextBlock Text="{Binding StatusIcon}"
                                                         FontSize="10"
                                                         Foreground="{Binding IsFromCurrentUser, Converter={x:Static local:Converters.MessageTimeForegroundConverter}}"
                                                         Margin="4,0,0,0"
                                                         Opacity="0.7"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Border>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- Typing Indicator -->
                <TextBlock Grid.Row="2"
                         Text="{Binding TypingIndicator}"
                         FontStyle="Italic"
                         FontSize="12"
                         Foreground="{StaticResource TextSecondaryBrush}"
                         Margin="16,4"
                         Visibility="{Binding TypingIndicator, Converter={x:Static local:Converters.StringToVisibilityConverter}}"/>

                <!-- Message Input -->
                <Border Grid.Row="3" Background="{StaticResource BackgroundBrush}" Padding="16" BorderBrush="{StaticResource DividerBrush}" BorderThickness="0,1,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0"
                               x:Name="MessageTextBox"
                               Text="{Binding MessageText, UpdateSourceTrigger=PropertyChanged}"
                               Style="{StaticResource ModernTextBoxStyle}"
                               AcceptsReturn="True"
                               MaxHeight="100"
                               VerticalScrollBarVisibility="Auto"
                               IsEnabled="{Binding SelectedUser, Converter={x:Static local:Converters.NotNullToBooleanConverter}}"
                               KeyDown="MessageTextBox_KeyDown"/>

                        <Button Grid.Column="1"
                              Content="Send"
                              Command="{Binding SendMessageCommand}"
                              Style="{StaticResource ModernButtonStyle}"
                              Margin="8,0,0,0"
                              IsEnabled="{Binding MessageText, Converter={x:Static local:Converters.StringToBooleanConverter}}"/>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</Window>
