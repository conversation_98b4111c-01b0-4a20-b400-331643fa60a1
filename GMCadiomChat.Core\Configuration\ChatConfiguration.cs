namespace GMCadiomChat.Core.Configuration;

public class ChatConfiguration
{
    public DatabaseSettings Database { get; set; } = new();
    public SignalRSettings SignalR { get; set; } = new();
    public FileSettings Files { get; set; } = new();
    public NotificationSettings Notifications { get; set; } = new();
}

public class DatabaseSettings
{
    public string Provider { get; set; } = "InMemory"; // InMemory, SqlServer, SQLite
    public string ConnectionString { get; set; } = string.Empty;
    public bool EnableSensitiveDataLogging { get; set; } = false;
    public bool EnableDetailedErrors { get; set; } = false;
}

public class SignalRSettings
{
    public string HubUrl { get; set; } = "http://localhost:5000/chathub";
    public int ReconnectDelaySeconds { get; set; } = 5;
    public int MaxReconnectAttempts { get; set; } = 10;
    public bool EnableDetailedErrors { get; set; } = false;
}

public class FileSettings
{
    public string UploadPath { get; set; } = "uploads";
    public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB
    public string[] AllowedImageFormats { get; set; } = { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
    public string[] AllowedAudioFormats { get; set; } = { ".mp3", ".wav", ".ogg", ".m4a" };
    public bool CreateDirectoryIfNotExists { get; set; } = true;
}

public class NotificationSettings
{
    public bool EnableDesktopNotifications { get; set; } = true;
    public bool EnableSounds { get; set; } = true;
    public bool EnableBuzzAnimation { get; set; } = true;
    public int NotificationDurationSeconds { get; set; } = 5;
    public string DefaultSoundPath { get; set; } = "sounds/notification.wav";
    public string BuzzSoundPath { get; set; } = "sounds/buzz.wav";
}
