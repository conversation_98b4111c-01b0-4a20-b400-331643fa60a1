using GMCadiomChat.Core.Interfaces;
using System;
using System.Media;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Animation;

namespace GMCadiomChat.Desktop.Services;

public class NotificationService : INotificationService
{
    private readonly Window? _mainWindow;

    public NotificationService(Window? mainWindow = null)
    {
        _mainWindow = mainWindow ?? Application.Current?.MainWindow;
    }

    public Task ShowNotificationAsync(string title, string message, string? iconPath = null)
    {
        return Application.Current.Dispatcher.InvokeAsync(() =>
        {
            // For now, we'll use a simple MessageBox
            // In a real application, you might want to use Windows Toast notifications
            // or a custom notification popup
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }).Task;
    }

    public Task ShowBuzzNotificationAsync(string fromUsername)
    {
        return Application.Current.Dispatcher.InvokeAsync(async () =>
        {
            // Show notification
            MessageBox.Show($"{fromUsername} sent you a buzz!", "Buzz!", MessageBoxButton.OK, MessageBoxImage.Exclamation);
            
            // Shake the window
            await ShakeWindowAsync();
            
            // Play buzz sound
            await PlaySoundAsync("buzz");
        }).Task;
    }

    public Task ShakeWindowAsync()
    {
        if (_mainWindow == null) return Task.CompletedTask;

        return Application.Current.Dispatcher.InvokeAsync(() =>
        {
            var originalLeft = _mainWindow.Left;
            var originalTop = _mainWindow.Top;

            var shakeAnimation = new DoubleAnimation
            {
                From = originalLeft,
                To = originalLeft + 10,
                Duration = TimeSpan.FromMilliseconds(50),
                AutoReverse = true,
                RepeatBehavior = new RepeatBehavior(5)
            };

            shakeAnimation.Completed += (s, e) =>
            {
                _mainWindow.Left = originalLeft;
                _mainWindow.Top = originalTop;
            };

            _mainWindow.BeginAnimation(Window.LeftProperty, shakeAnimation);
        }).Task;
    }

    public Task PlaySoundAsync(string soundType)
    {
        return Task.Run(() =>
        {
            try
            {
                switch (soundType.ToLower())
                {
                    case "buzz":
                        SystemSounds.Exclamation.Play();
                        break;
                    case "message":
                        SystemSounds.Asterisk.Play();
                        break;
                    case "notification":
                    default:
                        SystemSounds.Beep.Play();
                        break;
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - sound is not critical
                System.Diagnostics.Debug.WriteLine($"Failed to play sound: {ex.Message}");
            }
        });
    }
}
