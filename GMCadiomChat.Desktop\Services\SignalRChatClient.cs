using GMCadiomChat.Shared.DTOs;
using GMCadiomChat.Shared.Interfaces;
using Microsoft.AspNetCore.SignalR.Client;
using System;
using System.Threading.Tasks;

namespace GMCadiomChat.Desktop.Services;

public class SignalRChatClient : IChatHub, IAsyncDisposable
{
    private HubConnection? _connection;
    private readonly string _hubUrl;

    public SignalRChatClient(string hubUrl = "https://localhost:7248/chathub")
    {
        _hubUrl = hubUrl;
    }

    public event EventHandler<MessageDto>? MessageReceived;
    public event EventHandler<ClientDto>? UserConnected;
    public event EventHandler<ClientDto>? UserDisconnected;
    public event EventHandler<ClientDto>? UserStatusChanged;
    public event EventHandler<(Guid UserId, string Username)>? BuzzReceived;
    public event EventHandler<(Guid UserId, string Username, bool IsTyping)>? TypingIndicatorReceived;
    public event EventHandler<Guid>? MessageDelivered;
    public event EventHandler<Guid>? MessageRead;
    public event EventHandler<string>? ConnectionStatusChanged;

    public bool IsConnected => _connection?.State == HubConnectionState.Connected;

    public async Task ConnectAsync()
    {
        if (_connection != null)
        {
            await _connection.DisposeAsync();
        }

        _connection = new HubConnectionBuilder()
            .WithUrl(_hubUrl)
            .WithAutomaticReconnect()
            .Build();

        // Register event handlers
        _connection.On<MessageDto>("ReceiveMessage", (message) =>
        {
            MessageReceived?.Invoke(this, message);
        });

        _connection.On<ClientDto>("UserConnected", (client) =>
        {
            UserConnected?.Invoke(this, client);
        });

        _connection.On<ClientDto>("UserDisconnected", (client) =>
        {
            UserDisconnected?.Invoke(this, client);
        });

        _connection.On<ClientDto>("UserStatusChanged", (client) =>
        {
            UserStatusChanged?.Invoke(this, client);
        });

        _connection.On<Guid, string>("ReceiveBuzz", (fromUserId, fromUsername) =>
        {
            BuzzReceived?.Invoke(this, (fromUserId, fromUsername));
        });

        _connection.On<Guid, string, bool>("ReceiveTypingIndicator", (userId, username, isTyping) =>
        {
            TypingIndicatorReceived?.Invoke(this, (userId, username, isTyping));
        });

        _connection.On<Guid>("MessageDelivered", (messageId) =>
        {
            MessageDelivered?.Invoke(this, messageId);
        });

        _connection.On<Guid>("MessageRead", (messageId) =>
        {
            MessageRead?.Invoke(this, messageId);
        });

        // Connection state change handlers
        _connection.Closed += (error) =>
        {
            ConnectionStatusChanged?.Invoke(this, "Disconnected");
            return Task.CompletedTask;
        };

        _connection.Reconnecting += (error) =>
        {
            ConnectionStatusChanged?.Invoke(this, "Reconnecting");
            return Task.CompletedTask;
        };

        _connection.Reconnected += (connectionId) =>
        {
            ConnectionStatusChanged?.Invoke(this, "Connected");
            return Task.CompletedTask;
        };

        try
        {
            await _connection.StartAsync();
            ConnectionStatusChanged?.Invoke(this, "Connected");
        }
        catch (Exception ex)
        {
            ConnectionStatusChanged?.Invoke(this, $"Connection failed: {ex.Message}");
            throw;
        }
    }

    public async Task DisconnectAsync()
    {
        if (_connection != null)
        {
            await _connection.StopAsync();
            await _connection.DisposeAsync();
            _connection = null;
        }
    }

    // IChatHub implementation (these are called by the server)
    Task IChatHub.ReceiveMessage(MessageDto message)
    {
        MessageReceived?.Invoke(this, message);
        return Task.CompletedTask;
    }

    Task IChatHub.UserConnected(ClientDto client)
    {
        UserConnected?.Invoke(this, client);
        return Task.CompletedTask;
    }

    Task IChatHub.UserDisconnected(ClientDto client)
    {
        UserDisconnected?.Invoke(this, client);
        return Task.CompletedTask;
    }

    Task IChatHub.UserStatusChanged(ClientDto client)
    {
        UserStatusChanged?.Invoke(this, client);
        return Task.CompletedTask;
    }

    Task IChatHub.ReceiveBuzz(Guid fromUserId, string fromUsername)
    {
        BuzzReceived?.Invoke(this, (fromUserId, fromUsername));
        return Task.CompletedTask;
    }

    Task IChatHub.ReceiveTypingIndicator(Guid userId, string username, bool isTyping)
    {
        TypingIndicatorReceived?.Invoke(this, (userId, username, isTyping));
        return Task.CompletedTask;
    }

    Task IChatHub.MessageDelivered(Guid messageId)
    {
        MessageDelivered?.Invoke(this, messageId);
        return Task.CompletedTask;
    }

    Task IChatHub.MessageRead(Guid messageId)
    {
        MessageRead?.Invoke(this, messageId);
        return Task.CompletedTask;
    }

    // IChatServer methods (these are called by the client to the server)
    public async Task JoinChatAsync(LoginRequest request)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("JoinChat", request);
        }
    }

    public async Task LeaveChatAsync()
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("LeaveChat");
        }
    }

    public async Task SendMessageAsync(SendMessageRequest request)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("SendMessage", request);
        }
    }

    public async Task SendBuzzAsync(Guid toUserId)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("SendBuzz", toUserId);
        }
    }

    public async Task SetTypingAsync(Guid? toUserId, bool isTyping)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("SetTyping", toUserId, isTyping);
        }
    }

    public async Task MarkMessageAsReadAsync(Guid messageId)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("MarkMessageAsRead", messageId);
        }
    }

    public async Task GetOnlineUsersAsync()
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("GetOnlineUsers");
        }
    }

    public async Task GetMessageHistoryAsync(Guid? withUserId, int skip = 0, int take = 50)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("GetMessageHistory", withUserId, skip, take);
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_connection != null)
        {
            await _connection.DisposeAsync();
        }
    }
}
