﻿using System.Windows;
using System.Windows.Input;
using GMCadiomChat.Desktop.ViewModels;

namespace GMCadiomChat.Desktop;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();

        // Set focus to username textbox on startup
        Loaded += (s, e) => UsernameTextBox.Focus();
    }

    private void MessageTextBox_KeyDown(object sender, KeyEventArgs e)
    {
        // Send message on Enter (without Shift)
        if (e.Key == Key.Enter && !Keyboard.Modifiers.HasFlag(ModifierKeys.Shift))
        {
            e.Handled = true;

            if (DataContext is ChatViewModel viewModel && viewModel.SendMessageCommand.CanExecute(null))
            {
                viewModel.SendMessageCommand.Execute(null);
            }
        }
    }
}