{"format": 1, "restore": {"E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Server\\GMCadiomChat.Server.csproj": {}}, "projects": {"E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Core\\GMCadiomChat.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Projects\\<PERSON><PERSON>f <PERSON>by\\GMCadiomChat\\GMCadiomChat.Core\\GMCadiomChat.Core.csproj", "projectName": "GMCadiomChat.Core", "projectPath": "E:\\Projects\\<PERSON><PERSON>f <PERSON>by\\GMCadiomChat\\GMCadiomChat.Core\\GMCadiomChat.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Projects\\Sherif S<PERSON>\\GMCadiomChat\\GMCadiomChat.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Shared\\GMCadiomChat.Shared.csproj": {"projectPath": "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Shared\\GMCadiomChat.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Server\\GMCadiomChat.Server.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Server\\GMCadiomChat.Server.csproj", "projectName": "GMCadiomChat.Server", "projectPath": "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Server\\GMCadiomChat.Server.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Server\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Core\\GMCadiomChat.Core.csproj": {"projectPath": "E:\\Projects\\<PERSON><PERSON>f <PERSON>by\\GMCadiomChat\\GMCadiomChat.Core\\GMCadiomChat.Core.csproj"}, "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Shared\\GMCadiomChat.Shared.csproj": {"projectPath": "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Shared\\GMCadiomChat.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Shared\\GMCadiomChat.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Shared\\GMCadiomChat.Shared.csproj", "projectName": "GMCadiomChat.Shared", "projectPath": "E:\\Projects\\Sherif Shalaby\\GMCadiomChat\\GMCadiomChat.Shared\\GMCadiomChat.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Projects\\Sherif S<PERSON>\\GMCadiomChat\\GMCadiomChat.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}