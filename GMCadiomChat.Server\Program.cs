using GMCadiomChat.Core.Configuration;
using GMCadiomChat.Core.Extensions;
using GMCadiomChat.Server.Hubs;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add SignalR
builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = builder.Environment.IsDevelopment();
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add GMCadiomChat Core services
builder.Services.AddGMCadiomChatCore(config =>
{
    config.Database.Provider = "InMemory";
    config.Database.EnableDetailedErrors = builder.Environment.IsDevelopment();
    config.Database.EnableSensitiveDataLogging = builder.Environment.IsDevelopment();

    config.SignalR.EnableDetailedErrors = builder.Environment.IsDevelopment();
});

var app = builder.Build();

// Ensure database is created
await app.Services.EnsureDatabaseCreatedAsync();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAll");

app.UseRouting();
app.UseAuthorization();

app.MapControllers();
app.MapHub<ChatHub>("/chathub");

// Health check endpoint
app.MapGet("/health", () => Results.Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow }));

app.Run();
