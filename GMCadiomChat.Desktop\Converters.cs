using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using GMCadiomChat.Desktop.ViewModels;

namespace GMCadiomChat.Desktop;

public static class Converters
{
    public static readonly IValueConverter InverseBooleanConverter = new InverseBooleanValueConverter();
    public static readonly IValueConverter BooleanToVisibilityConverter = new BooleanToVisibilityValueConverter();
    public static readonly IValueConverter StringToVisibilityConverter = new StringToVisibilityValueConverter();
    public static readonly IValueConverter StringToBooleanConverter = new StringToBooleanValueConverter();
    public static readonly IValueConverter NotNullToBooleanConverter = new NotNullToBooleanValueConverter();
    public static readonly IValueConverter ConnectButtonTextConverter = new ConnectButtonTextValueConverter();
    public static readonly IValueConverter ConnectCommandConverter = new ConnectCommandValueConverter();
    public static readonly IValueConverter MessageAlignmentConverter = new MessageAlignmentValueConverter();
    public static readonly IValueConverter MessageBackgroundConverter = new MessageBackgroundValueConverter();
    public static readonly IValueConverter MessageForegroundConverter = new MessageForegroundValueConverter();
    public static readonly IValueConverter MessageTimeForegroundConverter = new MessageTimeForegroundValueConverter();
}

public class InverseBooleanValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is bool boolValue ? !boolValue : true;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is bool boolValue ? !boolValue : false;
    }
}

public class BooleanToVisibilityValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is bool boolValue && boolValue ? Visibility.Visible : Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is Visibility visibility && visibility == Visibility.Visible;
    }
}

public class StringToVisibilityValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return !string.IsNullOrWhiteSpace(value?.ToString()) ? Visibility.Visible : Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class StringToBooleanValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return !string.IsNullOrWhiteSpace(value?.ToString());
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class NotNullToBooleanValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value != null;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class ConnectButtonTextValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is bool isConnected && isConnected ? "Disconnect" : "Connect";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class ConnectCommandValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isConnected && Application.Current.MainWindow?.DataContext is ChatViewModel viewModel)
        {
            return isConnected ? viewModel.DisconnectCommand : viewModel.ConnectCommand;
        }
        return null!;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class MessageAlignmentValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is bool isFromCurrentUser && isFromCurrentUser ? HorizontalAlignment.Right : HorizontalAlignment.Left;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class MessageBackgroundValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is bool isFromCurrentUser && isFromCurrentUser 
            ? new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3)) // Primary blue
            : new SolidColorBrush(Color.FromRgb(0xE0, 0xE0, 0xE0)); // Light gray
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class MessageForegroundValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is bool isFromCurrentUser && isFromCurrentUser 
            ? Brushes.White 
            : new SolidColorBrush(Color.FromRgb(0x21, 0x21, 0x21)); // Dark text
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class MessageTimeForegroundValueConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is bool isFromCurrentUser && isFromCurrentUser 
            ? Brushes.White 
            : new SolidColorBrush(Color.FromRgb(0x75, 0x75, 0x75)); // Gray text
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
