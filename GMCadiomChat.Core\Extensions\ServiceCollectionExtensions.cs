using GMCadiomChat.Core.Configuration;
using GMCadiomChat.Core.Data;
using GMCadiomChat.Core.Interfaces;
using GMCadiomChat.Core.Repositories;
using GMCadiomChat.Core.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace GMCadiomChat.Core.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddGMCadiomChatCore(this IServiceCollection services, ChatConfiguration configuration)
    {
        // Add configuration
        services.AddSingleton(configuration);

        // Add DbContext based on configuration
        services.AddDbContext<ChatDbContext>(options =>
        {
            switch (configuration.Database.Provider.ToLower())
            {
                case "sqlserver":
                    options.UseSqlServer(configuration.Database.ConnectionString);
                    break;
                case "sqlite":
                    options.UseSqlite(configuration.Database.ConnectionString);
                    break;
                case "inmemory":
                default:
                    options.UseInMemoryDatabase("GMCadiomChatDb");
                    break;
            }

            if (configuration.Database.EnableSensitiveDataLogging)
                options.EnableSensitiveDataLogging();

            if (configuration.Database.EnableDetailedErrors)
                options.EnableDetailedErrors();
        });

        // Add repositories
        services.AddScoped<IClientRepository, ClientRepository>();
        services.AddScoped<IMessageRepository, MessageRepository>();

        // Add services
        services.AddScoped<IChatService, ChatService>();

        return services;
    }

    public static IServiceCollection AddGMCadiomChatCore(this IServiceCollection services, Action<ChatConfiguration> configureOptions)
    {
        var configuration = new ChatConfiguration();
        configureOptions(configuration);
        return services.AddGMCadiomChatCore(configuration);
    }

    public static async Task<IServiceProvider> EnsureDatabaseCreatedAsync(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ChatDbContext>();
        await context.Database.EnsureCreatedAsync();
        return serviceProvider;
    }
}
